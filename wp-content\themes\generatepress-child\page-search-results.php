<?php
/**
 * Search Results Page Template
 * Template Name: Search Results
 */

get_header(); ?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">
        
        <!-- Hero Section -->
        <section class="hero-section search-results-hero">
            <div class="hero-content">
                <div class="container">
                    <h1 id="search-results-title">Search Results</h1>
                    <div class="search-info">
                        <span id="results-count"></span>
                    </div>
                    
                    <!-- Search Form -->
                    <form id="search-form" class="search-form">
                        <div class="search-input-container">
                            <input type="text" id="search-input" name="search" placeholder="Enter city or state name..." autocomplete="off">
                            <div id="search-suggestions" class="search-suggestions"></div>
                        </div>
                        <button type="submit" class="btn btn-primary">Search</button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Search Results Section -->
        <section class="search-results-section">
            <div class="search-results-container">
                
                <!-- Left Side - Google Maps -->
                <div class="search-results-left">
                    <div class="map-container">
                        <div id="search-map" style="width: 100%; height: 600px;"></div>
                        <div class="map-loading" id="map-loading">
                            <div class="loading-spinner"></div>
                            <p>Loading map...</p>
                        </div>
                    </div>
                </div>

                <!-- Right Side - Petting Zoo Cards -->
                <div class="search-results-right">
                    <div class="results-header">
                        <h2 id="petting-zoos-found-title">Petting Zoos Found</h2>
                        <div class="filters-container">
                            <select id="animal-filter" name="animal_type">
                                <option value="">All Animals</option>
                                <?php
                                $animal_types = get_terms(array(
                                    'taxonomy' => 'animal_type',
                                    'hide_empty' => false
                                ));
                                foreach ($animal_types as $animal) {
                                    echo '<option value="' . esc_attr($animal->slug) . '">' . esc_html($animal->name) . '</option>';
                                }
                                ?>
                            </select>
                            
                            <select id="zoo-type-filter" name="zoo_type">
                                <option value="">All Types</option>
                                <?php
                                $zoo_types = get_terms(array(
                                    'taxonomy' => 'zoo_type',
                                    'hide_empty' => false
                                ));
                                foreach ($zoo_types as $type) {
                                    echo '<option value="' . esc_attr($type->slug) . '">' . esc_html($type->name) . '</option>';
                                }
                                ?>
                            </select>
                            
                            <select id="features-filter" name="features">
                                <option value="">All Features</option>
                                <?php
                                $features = get_terms(array(
                                    'taxonomy' => 'features',
                                    'hide_empty' => false
                                ));
                                foreach ($features as $feature) {
                                    echo '<option value="' . esc_attr($feature->slug) . '">' . esc_html($feature->name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    
                    <div id="search-results-grid" class="petting-zoo-cards-grid">
                        <div class="loading-message" id="results-loading">
                            <div class="loading-spinner"></div>
                            <p>Searching for petting zoos...</p>
                        </div>
                    </div>
                    
                    <div id="no-results" class="no-results-message" style="display: none;">
                        <h3>No petting zoos found</h3>
                        <p>Try searching for a different location or adjusting your filters.</p>
                    </div>
                </div>
                
            </div>
        </section>

    </main>
</div>

<style>
/* Search Results Specific Styles */
.search-results-hero {
    background: linear-gradient(135deg, var(--forest-green) 0%, var(--sky-blue) 100%);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.search-info {
    margin: 1rem 0;
    font-size: 1.1rem;
}

.search-form {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    max-width: 600px;
    margin: 0 auto;
}

.search-input-container {
    position: relative;
    flex: 1;
}

.search-input-container input {
    width: 100%;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    z-index: 1000;
    display: none;
    max-height: 300px;
    overflow-y: auto;
}

.search-suggestion {
    padding: 12px 16px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.search-suggestion:hover {
    background-color: #f5f5f5;
}

.search-suggestion:last-child {
    border-bottom: none;
}

.search-results-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

.search-results-left {
    position: relative;
}

.map-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.map-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--forest-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.results-header {
    margin-bottom: 1.5rem;
}

.filters-container {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.filters-container select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    font-size: 14px;
}

.loading-message, .no-results-message {
    text-align: center;
    padding: 3rem;
    color: var(--medium-gray);
}

.no-results-message h3 {
    color: var(--forest-green);
    margin-bottom: 1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .search-results-container {
        grid-template-columns: 1fr;
        padding: 1rem;
    }
    
    .search-form {
        flex-direction: column;
        gap: 1rem;
    }
    
    .search-input-container {
        width: 100%;
    }
    
    .filters-container {
        flex-direction: column;
    }
    
    .filters-container select {
        width: 100%;
    }
}
</style>

<script>
console.log('Search Results Page: Initializing...');

// Ensure pettingZooAjax is available
if (typeof pettingZooAjax === 'undefined') {
    window.pettingZooAjax = {
        ajaxurl: '<?php echo admin_url('admin-ajax.php'); ?>',
        nonce: '<?php echo wp_create_nonce('petting_zoo_nonce'); ?>'
    };
}

// Global variables
let searchMap;
let searchMarkers = [];
let searchResults = [];

// Initialize page
jQuery(document).ready(function($) {
    console.log('Search Results Page: DOM ready');
    
    // Get search parameters from URL
    const urlParams = new URLSearchParams(window.location.search);
    const searchQuery = urlParams.get('search') || '';
    const lat = urlParams.get('lat');
    const lng = urlParams.get('lng');
    
    console.log('Search Results Page: Search query:', searchQuery);
    console.log('Search Results Page: Coordinates:', lat, lng);
    
    // Update search input and display
    if (searchQuery) {
        jQuery('#search-input').val(searchQuery);
        jQuery('#search-results-title').text('Search Results for "' + searchQuery + '"');
        jQuery('#petting-zoos-found-title').text('Petting Zoos Found in ' + searchQuery);
    }

    // Initialize map
    initializeSearchMap(lat, lng);

    // Perform initial search
    if (searchQuery) {
        performSearch(searchQuery, lat, lng);
    }
    
    // Handle search form submission
    jQuery('#search-form').on('submit', function(e) {
        e.preventDefault();
        const query = jQuery('#search-input').val().trim();
        if (query) {
            console.log('Search Results Page: New search submitted:', query);
            performSearch(query);
            updateURL(query);
        }
    });

    // Handle filter changes
    jQuery('.filters-container select').on('change', function() {
        console.log('Search Results Page: Filter changed');
        applyFilters();
    });

    // Search input autocomplete
    jQuery('#search-input').on('input', function() {
        const query = jQuery(this).val();
        if (query.length >= 2) {
            console.log('Search Results Page: Autocomplete query:', query);
            fetchSearchSuggestions(query);
        } else {
            jQuery('#search-suggestions').hide();
        }
    });

    // Handle suggestion clicks
    jQuery(document).on('click', '.search-suggestion', function() {
        const suggestion = jQuery(this).text();
        const lat = jQuery(this).data('lat');
        const lng = jQuery(this).data('lng');

        console.log('Search Results Page: Suggestion selected:', suggestion);

        jQuery('#search-input').val(suggestion);
        jQuery('#search-suggestions').hide();
        performSearch(suggestion, lat, lng);
        updateURL(suggestion);
    });

    // Hide suggestions when clicking outside
    jQuery(document).click(function(e) {
        if (!jQuery(e.target).closest('.search-input-container').length) {
            jQuery('#search-suggestions').hide();
        }
    });
});

// Initialize Google Maps
function initializeSearchMap(lat, lng) {
    console.log('Search Results Page: Initializing map with coordinates:', lat, lng);

    // Default center (US center)
    const defaultCenter = { lat: 39.8283, lng: -98.5795 };
    const center = (lat && lng) ? { lat: parseFloat(lat), lng: parseFloat(lng) } : defaultCenter;

    searchMap = new google.maps.Map(document.getElementById('search-map'), {
        zoom: (lat && lng) ? 10 : 4,
        center: center,
        styles: [
            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }]
            }
        ]
    });

    // Hide loading overlay
    jQuery('#map-loading').fadeOut();

    console.log('Search Results Page: Map initialized');
}

// Perform search for petting zoos
function performSearch(query, lat, lng) {
    console.log('Search Results Page: Performing search for:', query);

    jQuery('#results-loading').show();
    jQuery('#no-results').hide();
    jQuery('#search-results-grid .petting-zoo-card').remove();

    // Clear existing markers
    clearMapMarkers();

    // Update titles
    jQuery('#search-results-title').text('Search Results for "' + query + '"');
    jQuery('#petting-zoos-found-title').text('Petting Zoos Found in ' + query);

    // AJAX search request
    jQuery.ajax({
        url: '<?php echo admin_url('admin-ajax.php'); ?>',
        type: 'POST',
        data: {
            action: 'search_petting_zoos',
            query: query,
            lat: lat,
            lng: lng,
            nonce: pettingZooAjax.nonce
        },
        success: function(response) {
            console.log('Search Results Page: Search response:', response);

            jQuery('#results-loading').hide();

            if (response.success && response.data.zoos.length > 0) {
                searchResults = response.data.zoos;
                displaySearchResults(searchResults);
                updateResultsCount(searchResults.length);

                // Try to add map markers (don't fail if map doesn't work)
                try {
                    addMapMarkers(searchResults);
                    // Center map on results
                    if (searchResults.length > 0) {
                        centerMapOnResults(searchResults);
                    }
                } catch (mapError) {
                    console.warn('Search Results Page: Map error (continuing without map):', mapError);
                }
            } else {
                jQuery('#no-results').show();
                updateResultsCount(0);
            }
        },
        error: function(xhr, status, error) {
            console.error('Search Results Page: Search error:', error);
            jQuery('#results-loading').hide();
            jQuery('#no-results').show();
            updateResultsCount(0);
        }
    });
}

// Display search results as cards
function displaySearchResults(zoos) {
    console.log('Search Results Page: Displaying', zoos.length, 'results');

    const grid = jQuery('#search-results-grid');

    zoos.forEach(function(zoo) {
        const card = createZooCard(zoo);
        grid.append(card);
    });
}

// Create zoo card HTML
function createZooCard(zoo) {
    const imageUrl = zoo.image || '/wp-content/uploads/2025/06/placeholder.jpg';
    const rating = zoo.rating ? parseFloat(zoo.rating).toFixed(1) : '';
    const ratingStars = rating ? '★'.repeat(Math.floor(rating)) : '';

    return `
        <div class="petting-zoo-card" data-zoo-id="${zoo.id}" data-lat="${zoo.lat}" data-lng="${zoo.lng}">
            <div class="zoo-image-container">
                <img src="${imageUrl}" alt="${zoo.name}" class="zoo-image" loading="lazy">
                ${rating ? `<div class="rating-badge">${rating} ${ratingStars}</div>` : ''}
            </div>
            <div class="zoo-content">
                <h3 class="zoo-name">${zoo.name}</h3>
                <p class="zoo-address"><i class="fas fa-map-marker-alt"></i> ${zoo.address}</p>
                ${zoo.distance ? `<p class="zoo-distance">${zoo.distance} miles away</p>` : ''}
                <div class="zoo-features">
                    ${zoo.features.map(feature => `<span class="feature-badge">${feature}</span>`).join('')}
                </div>
                <p class="zoo-excerpt">${zoo.excerpt}</p>
                <a href="${zoo.url}" class="btn btn-primary zoo-details-btn">View Details →</a>
            </div>
        </div>
    `;
}

// Add markers to map
function addMapMarkers(zoos) {
    console.log('Search Results Page: Adding', zoos.length, 'markers to map');

    zoos.forEach(function(zoo, index) {
        if (zoo.lat && zoo.lng) {
            const marker = new google.maps.Marker({
                position: { lat: parseFloat(zoo.lat), lng: parseFloat(zoo.lng) },
                map: searchMap,
                title: zoo.name,
                icon: {
                    url: '/wp-content/uploads/2025/06/map-marker.png',
                    scaledSize: new google.maps.Size(40, 40)
                }
            });

            // Info window
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div class="map-info-window">
                        <h4>${zoo.name}</h4>
                        <p>${zoo.address}</p>
                        <a href="${zoo.url}" class="btn btn-sm btn-primary">View Details</a>
                    </div>
                `
            });

            marker.addListener('click', function() {
                // Close other info windows
                searchMarkers.forEach(m => m.infoWindow.close());
                infoWindow.open(searchMap, marker);

                // Highlight corresponding card
                jQuery('.petting-zoo-card').removeClass('highlighted');
                jQuery(`[data-zoo-id="${zoo.id}"]`).addClass('highlighted');
            });

            marker.infoWindow = infoWindow;
            searchMarkers.push(marker);
        }
    });
}

// Clear map markers
function clearMapMarkers() {
    console.log('Search Results Page: Clearing map markers');
    searchMarkers.forEach(marker => marker.setMap(null));
    searchMarkers = [];
}

// Center map on results
function centerMapOnResults(zoos) {
    if (zoos.length === 0) return;

    const bounds = new google.maps.LatLngBounds();

    zoos.forEach(function(zoo) {
        if (zoo.lat && zoo.lng) {
            bounds.extend(new google.maps.LatLng(parseFloat(zoo.lat), parseFloat(zoo.lng)));
        }
    });

    searchMap.fitBounds(bounds);

    // Ensure minimum zoom level
    google.maps.event.addListenerOnce(searchMap, 'bounds_changed', function() {
        if (searchMap.getZoom() > 15) {
            searchMap.setZoom(15);
        }
    });
}

// Update results count
function updateResultsCount(count) {
    const countText = count === 0 ? 'No results found' : `${count} petting zoo${count !== 1 ? 's' : ''} found`;
    jQuery('#results-count').text(countText);
}

// Fetch search suggestions
function fetchSearchSuggestions(query) {
    jQuery.ajax({
        url: '<?php echo admin_url('admin-ajax.php'); ?>',
        type: 'POST',
        data: {
            action: 'get_search_suggestions',
            query: query,
            nonce: pettingZooAjax.nonce
        },
        success: function(response) {
            if (response.success && response.data.length > 0) {
                displaySearchSuggestions(response.data);
            } else {
                jQuery('#search-suggestions').hide();
            }
        },
        error: function() {
            jQuery('#search-suggestions').hide();
        }
    });
}

// Display search suggestions
function displaySearchSuggestions(suggestions) {
    const suggestionsHtml = suggestions.map(suggestion =>
        `<div class="search-suggestion" data-lat="${suggestion.lat}" data-lng="${suggestion.lng}">
            <i class="fas fa-map-marker-alt"></i> ${suggestion.name}
        </div>`
    ).join('');

    jQuery('#search-suggestions').html(suggestionsHtml).show();
}

// Apply filters to current results
function applyFilters() {
    const animalFilter = jQuery('#animal-filter').val();
    const zooTypeFilter = jQuery('#zoo-type-filter').val();
    const featuresFilter = jQuery('#features-filter').val();

    console.log('Search Results Page: Applying filters:', { animalFilter, zooTypeFilter, featuresFilter });

    let filteredResults = searchResults;

    if (animalFilter) {
        filteredResults = filteredResults.filter(zoo =>
            zoo.animal_types && zoo.animal_types.includes(animalFilter)
        );
    }

    if (zooTypeFilter) {
        filteredResults = filteredResults.filter(zoo =>
            zoo.zoo_types && zoo.zoo_types.includes(zooTypeFilter)
        );
    }

    if (featuresFilter) {
        filteredResults = filteredResults.filter(zoo =>
            zoo.features_slugs && zoo.features_slugs.includes(featuresFilter)
        );
    }

    // Clear and redisplay results
    jQuery('#search-results-grid .petting-zoo-card').remove();
    clearMapMarkers();

    if (filteredResults.length > 0) {
        displaySearchResults(filteredResults);
        addMapMarkers(filteredResults);
        centerMapOnResults(filteredResults);
        jQuery('#no-results').hide();
    } else {
        jQuery('#no-results').show();
    }

    updateResultsCount(filteredResults.length);
}

// Update URL with search parameters
function updateURL(query) {
    const newUrl = new URL(window.location);
    newUrl.searchParams.set('search', query);
    window.history.pushState({}, '', newUrl);
}
</script>

<?php get_footer(); ?>
